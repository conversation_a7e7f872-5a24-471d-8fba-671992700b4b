import { getConversationsListAPI } from 'widget/api/conversation';

export const actions = {
  fetchConversations: async ({ commit, state }, { page = 1, per_page = 5, reset = false } = {}) => {
    const fetchingAction = reset || page === 1 ? 'setFetching' : 'setFetchingMore';

    commit(fetchingAction, true);
    try {
      const { data } = await getConversationsListAPI({ page, per_page });

      if (reset || page === 1) {
        commit('setConversations', data.conversations || data);
      } else {
        commit('appendConversations', data.conversations || data);
      }

      // Handle pagination metadata if available
      if (data.meta) {
        commit('setMeta', {
          currentPage: data.meta.current_page || page,
          totalPages: data.meta.total_pages || 1,
          hasMorePages: data.meta.current_page < data.meta.total_pages,
        });
      } else {
        // Fallback: assume no more pages if we get less than 20 items
        const conversationCount = (data.conversations || data).length;
        commit('setMeta', {
          currentPage: page,
          hasMorePages: conversationCount >= per_page,
        });
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);
    } finally {
      commit(fetchingAction, false);
    }
  },

  loadMoreConversations: async ({ dispatch, state }) => {
    if (!state.meta.hasMorePages || state.uiFlags.isFetching || state.uiFlags.isFetchingMore) {
      return;
    }

    const nextPage = state.meta.currentPage + 1;
    await dispatch('fetchConversations', { page: nextPage, reset: false });
  },

  clearConversations: ({ commit }) => {
    commit('setConversations', []);
    commit('setMeta', {
      currentPage: 1,
      totalPages: 1,
      hasMorePages: false,
    });
  },
};
