<script>
import CustomButton from 'shared/components/Button.vue';
import TeamAvailability from 'widget/components/TeamAvailability.vue';
import ConversationsList from 'widget/components/ConversationsList.vue';
import { mapGetters, mapActions } from 'vuex';
import routerMixin from 'widget/mixins/routerMixin';
import configMixin from 'widget/mixins/configMixin';
import { getContrastingTextColor } from '@chatwoot/utils';
import { IFrameHelper } from '../helpers/utils';
import { CHATWOOT_ON_START_CONVERSATION } from '../constants/sdkEvents';
import ArticleContainer from '../components/pageComponents/Home/Article/ArticleContainer.vue';
export default {
  name: 'Home',
  components: {
    ArticleContainer,
    TeamAvailability,
    CustomButton,
    ConversationsList
  },
  mixins: [configMixin, routerMixin],
  computed: {
    ...mapGetters({
      availableAgents: 'agent/availableAgents',
      conversationSize: 'conversation/getConversationSize',
      unreadMessageCount: 'conversation/getUnreadMessageCount',
      widgetColor: 'appConfig/getWidgetColor',
    }),


    textColor() {
      return getContrastingTextColor(this.widgetColor);
    },
  },
  methods: {
    ...mapActions('conversation', [
      'clearConversations',
    ]),
    ...mapActions('conversationAttributes', [
      'clearConversationAttributes',
    ]),
    startConversation() {
      if (this.preChatFormEnabled && !this.conversationSize) {
        return this.replaceRoute('prechat-form');
      }
      return this.replaceRoute('messages');
    },

    startNewConversation() {
      this.clearConversations();
      this.clearConversationAttributes();
      this.replaceRoute('messages');
      IFrameHelper.sendMessage({
        event: 'onEvent',
        eventIdentifier: CHATWOOT_ON_START_CONVERSATION,
        data: { hasConversation: true },
      });
    },

    selectConversation(conversation) {
      // Load the selected conversation and navigate to messages
      this.$store.commit('conversation/setConversationListLoading', true);
      this.$store.dispatch('conversation/loadConversation', conversation.id);
      this.replaceRoute('messages');
    },
  },
};
</script>

<template>
  <div class="z-50 flex flex-col justify-between flex-1 w-full p-4 ">
    <!-- Conversations List -->
    <div class="conversations-section mb-4 max-h-100 overflow-hidden">
      <ConversationsList @select-conversation="selectConversation" />
    </div>

    <CustomButton
      class="font-medium"
      block
      :bg-color="widgetColor"
      :text-color="textColor"
      @click="startNewConversation"
    >
      {{ $t('START_NEW_CONVERSATION') }}
    </CustomButton>

  </div>
</template>
