json.conversations @conversations do |conversation|
  json.id conversation.display_id
  json.uuid conversation.uuid
  json.inbox_id conversation.inbox_id
  json.status conversation.status
  json.assignee_last_seen_at conversation.assignee_last_seen_at&.to_i
  json.contact_last_seen_at conversation.contact_last_seen_at&.to_i
  json.last_activity_at conversation.last_activity_at&.to_i
  json.created_at conversation.created_at&.to_i
  json.updated_at conversation.updated_at&.to_i

  if conversation.contact_inbox.present?
    json.contact_inbox do
      json.source_id conversation.contact_inbox.source_id
    end
  else
    json.contact_inbox nil
  end


  if conversation.assignee.present?
    json.assignee do
      json.id conversation.assignee.id
      json.name conversation.assignee.name
      json.avatar_url conversation.assignee.avatar_url
    end
  else
    json.assignee nil
  end

  # Get the last message for preview
  last_message = conversation.messages.order(created_at: :desc).first
  if last_message.present?
    json.last_message do
      json.id last_message.id
      json.content last_message.content
      json.message_type last_message.message_type
      json.created_at last_message.created_at&.to_i
    end
  else
    json.last_message nil
  end
end

if @pagination_meta.present?
  json.meta do
    json.current_page @pagination_meta[:current_page]
    json.total_pages @pagination_meta[:total_pages]
    json.total_count @pagination_meta[:total_count]
    json.per_page @pagination_meta[:per_page]
  end
end
